# 项目结构自动创建工具

这个工具可以基于 `项目目录结构.md` 文档自动创建完整的项目目录结构和基础代码文件。

## 功能特点

- 🚀 **一键创建**: 根据markdown文档自动生成项目结构
- 📁 **智能解析**: 自动解析树形目录结构
- 📝 **模板生成**: 为不同类型的文件生成相应的代码模板
- 🔧 **可配置**: 支持自定义输出目录和结构文档
- 👀 **预览模式**: 支持dry-run模式预览将要创建的文件

## 使用方法

### 基本用法

```bash
# 在当前目录创建项目结构
python create_project_structure.py

# 指定输出目录
python create_project_structure.py --output-dir ./my_project

# 使用自定义结构文档
python create_project_structure.py --structure-file my_structure.md

# 预览模式（不实际创建文件）
python create_project_structure.py --dry-run
```

### 参数说明

- `--structure-file, -s`: 项目结构文档文件路径（默认: `项目目录结构.md`）
- `--output-dir, -o`: 输出目录路径（默认: 当前目录）
- `--dry-run, -d`: 仅显示将要创建的文件，不实际创建

## 支持的文件模板

工具会为以下文件自动生成相应的代码模板：

### Python文件
- `__init__.py`: 空文件
- `main.py`: 程序入口模板
- `requirements.txt`: 依赖包列表

### 核心模块
- `src/core/excel_reader.py`: Excel读取器模板
- `src/core/excel_writer.py`: Excel写入器模板
- `src/core/data_processor.py`: 数据处理器模板

### 工具模块
- `src/utils/config.py`: 配置管理器模板
- `src/utils/logger.py`: 日志处理器模板
- `src/utils/validators.py`: 数据验证器模板

### GUI模块
- `src/gui/main_window.py`: 主窗口界面模板

### 处理器模块
- `src/processors/*.py`: 各种数据处理器的基础模板

## 项目结构文档格式

工具支持标准的树形目录结构格式，例如：

```
excel2excel/
├── src/
│   ├── __init__.py
│   ├── core/
│   │   ├── __init__.py
│   │   ├── excel_reader.py      # Excel文件读取相关
│   │   ├── excel_writer.py      # Excel文件写入相关
│   │   └── data_processor.py    # 数据处理核心逻辑
│   └── utils/
│       ├── __init__.py
│       ├── config.py           # 配置文件
│       └── logger.py           # 日志处理
├── tests/
│   ├── __init__.py
│   └── test_*.py
├── requirements.txt
└── main.py                     # 程序入口
```

### 格式要求

1. 使用标准的树形字符（├── └── │）
2. 目录名以 `/` 结尾
3. 可以在文件名后添加 `# 注释` 来描述文件用途
4. 支持多级嵌套目录

## 示例

### 1. 预览将要创建的文件

```bash
python create_project_structure.py --dry-run
```

输出：
```
将要创建的文件结构:
  src/__init__.py # 
  src/core/__init__.py # 
  src/core/excel_reader.py # Excel文件读取相关
  src/core/excel_writer.py # Excel文件写入相关
  ...
```

### 2. 创建项目结构

```bash
python create_project_structure.py --output-dir ./excel2excel_project
```

输出：
```
开始创建项目结构到: ./excel2excel_project
创建文件: ./excel2excel_project/src/__init__.py
创建文件: ./excel2excel_project/src/core/__init__.py
创建文件: ./excel2excel_project/src/core/excel_reader.py
...
项目结构创建完成!
```

## 注意事项

1. 如果目标文件已存在，工具会跳过创建，不会覆盖现有文件
2. 工具会自动创建必要的父目录
3. 生成的代码模板包含基础结构，需要根据具体需求进行完善
4. 建议在使用前先用 `--dry-run` 参数预览要创建的文件

## 扩展

如果需要添加新的文件模板，可以在 `ProjectStructureCreator` 类中：

1. 在 `specific_templates` 字典中添加特定文件的模板
2. 在 `base_templates` 字典中添加通用文件的模板
3. 创建对应的 `_get_xxx_template()` 方法

## 依赖

- Python 3.6+
- 标准库模块：os, re, argparse, pathlib, typing
