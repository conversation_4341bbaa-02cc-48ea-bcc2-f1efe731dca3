#!/usr/bin/env python3
"""
项目结构自动创建工具
基于项目目录结构.md文档自动创建项目目录和文件
"""

import os
import re
import argparse
from pathlib import Path
from typing import List, Dict, Tuple


class ProjectStructureCreator:
    """项目结构创建器"""
    
    def __init__(self, structure_file: str = "项目目录结构.md"):
        self.structure_file = structure_file
        self.base_templates = {
            '__init__.py': '',
            'requirements.txt': self._get_requirements_template(),
            'main.py': self._get_main_template(),
        }
        self.specific_templates = {
            'src/core/excel_reader.py': self._get_excel_reader_template(),
            'src/core/excel_writer.py': self._get_excel_writer_template(),
            'src/core/data_processor.py': self._get_data_processor_template(),
            'src/utils/config.py': self._get_config_template(),
            'src/utils/logger.py': self._get_logger_template(),
            'src/utils/validators.py': self._get_validators_template(),
            'src/gui/main_window.py': self._get_gui_template(),
        }
    
    def parse_structure_file(self) -> List[Tuple[str, str, str]]:
        """
        解析项目结构文档
        返回: [(路径, 文件名, 注释), ...]
        """
        if not os.path.exists(self.structure_file):
            raise FileNotFoundError(f"结构文档 {self.structure_file} 不存在")
        
        with open(self.structure_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        items = []
        current_path = []
        
        for line in lines:
            if not line.strip():
                continue
                
            # 解析树形结构
            level = self._get_tree_level(line)
            name_part = self._extract_name_and_comment(line)
            
            if not name_part:
                continue
                
            name, comment = name_part
            
            # 调整当前路径深度
            current_path = current_path[:level]
            
            if name.endswith('/'):
                # 目录
                dir_name = name.rstrip('/')
                current_path.append(dir_name)
            else:
                # 文件
                file_path = '/'.join(current_path) if current_path else ''
                items.append((file_path, name, comment))
        
        return items
    
    def _get_tree_level(self, line: str) -> int:
        """获取树形结构的层级"""
        # 计算缩进级别
        stripped = line.lstrip()
        indent = len(line) - len(stripped)
        
        # 移除树形字符
        clean_line = re.sub(r'^[│├└─\s]*', '', stripped)
        
        # 根据缩进和树形字符估算层级
        if '├──' in line or '└──' in line:
            return line.count('│') + line.count('├') + line.count('└')
        
        return indent // 4  # 假设每级缩进4个空格
    
    def _extract_name_and_comment(self, line: str) -> Tuple[str, str]:
        """提取文件/目录名和注释"""
        # 清理树形字符
        clean_line = re.sub(r'^[│├└─\s]*', '', line.strip())
        
        if not clean_line:
            return None
        
        # 分离名称和注释
        if '#' in clean_line:
            name, comment = clean_line.split('#', 1)
            return name.strip(), comment.strip()
        else:
            return clean_line.strip(), ''
    
    def create_structure(self, base_dir: str = None):
        """创建项目结构"""
        if base_dir is None:
            base_dir = os.getcwd()
        
        base_path = Path(base_dir)
        items = self.parse_structure_file()
        
        print(f"开始创建项目结构到: {base_path}")
        
        # 创建目录和文件
        for file_path, file_name, comment in items:
            full_path = base_path / file_path if file_path else base_path
            file_full_path = full_path / file_name
            
            # 创建目录
            full_path.mkdir(parents=True, exist_ok=True)
            
            # 创建文件
            if not file_full_path.exists():
                content = self._get_file_content(file_path, file_name, comment)
                with open(file_full_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"创建文件: {file_full_path}")
            else:
                print(f"文件已存在，跳过: {file_full_path}")
    
    def _get_file_content(self, file_path: str, file_name: str, comment: str) -> str:
        """获取文件内容"""
        # 构建相对路径
        if file_path:
            relative_path = f"{file_path}/{file_name}"
        else:
            relative_path = file_name
        
        # 检查特定模板
        if relative_path in self.specific_templates:
            return self.specific_templates[relative_path]
        
        # 检查基础模板
        if file_name in self.base_templates:
            return self.base_templates[file_name]
        
        # 根据文件类型生成基础内容
        if file_name.endswith('.py'):
            return self._get_python_template(file_name, comment)
        
        return ''
    
    def _get_python_template(self, file_name: str, comment: str) -> str:
        """生成Python文件模板"""
        module_name = file_name.replace('.py', '').replace('_', ' ').title()
        
        template = f'''"""
{module_name}
{comment if comment else f'{module_name}相关功能'}
"""

# TODO: 实现{module_name}相关功能
'''
        return template

    def _get_requirements_template(self) -> str:
        """生成requirements.txt模板"""
        return """# Excel处理相关
pandas>=1.5.0
openpyxl>=3.0.0
xlrd>=2.0.0

# GUI相关
tkinter

# 日志和配置
pyyaml>=6.0

# 测试相关
pytest>=7.0.0
pytest-cov>=4.0.0

# 开发工具
black>=22.0.0
flake8>=5.0.0
"""

    def _get_main_template(self) -> str:
        """生成main.py模板"""
        return '''#!/usr/bin/env python3
"""
Excel2Excel 主程序入口
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.gui.main_window import MainWindow
from src.utils.logger import setup_logger


def main():
    """主函数"""
    # 设置日志
    logger = setup_logger()
    logger.info("启动Excel2Excel应用程序")

    try:
        # 启动GUI应用
        app = MainWindow()
        app.run()
    except Exception as e:
        logger.error(f"应用程序运行出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
'''

    def _get_excel_reader_template(self) -> str:
        """生成excel_reader.py模板"""
        return '''"""
Excel Reader
Excel文件读取相关功能
"""

import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional
from ..utils.logger import get_logger

logger = get_logger(__name__)


class ExcelReader:
    """Excel文件读取器"""

    def __init__(self):
        self.supported_formats = ['.xlsx', '.xls', '.xlsm']

    def read_excel(self, file_path: str, sheet_name: Optional[str] = None) -> pd.DataFrame:
        """
        读取Excel文件

        Args:
            file_path: Excel文件路径
            sheet_name: 工作表名称，None表示读取第一个工作表

        Returns:
            DataFrame: 读取的数据
        """
        try:
            file_path = Path(file_path)

            if not file_path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")

            if file_path.suffix not in self.supported_formats:
                raise ValueError(f"不支持的文件格式: {file_path.suffix}")

            logger.info(f"开始读取Excel文件: {file_path}")

            # 读取Excel文件
            df = pd.read_excel(file_path, sheet_name=sheet_name)

            logger.info(f"成功读取Excel文件，数据形状: {df.shape}")
            return df

        except Exception as e:
            logger.error(f"读取Excel文件失败: {e}")
            raise

    def get_sheet_names(self, file_path: str) -> List[str]:
        """
        获取Excel文件中的所有工作表名称

        Args:
            file_path: Excel文件路径

        Returns:
            List[str]: 工作表名称列表
        """
        try:
            excel_file = pd.ExcelFile(file_path)
            return excel_file.sheet_names
        except Exception as e:
            logger.error(f"获取工作表名称失败: {e}")
            raise

    def _get_excel_writer_template(self) -> str:
        """生成excel_writer.py模板"""
        return '''"""
Excel Writer
Excel文件写入相关功能
"""

import pandas as pd
from pathlib import Path
from typing import Dict, Optional
from ..utils.logger import get_logger

logger = get_logger(__name__)


class ExcelWriter:
    """Excel文件写入器"""

    def __init__(self):
        pass

    def write_excel(self, data: pd.DataFrame, file_path: str,
                   sheet_name: str = 'Sheet1', index: bool = False) -> None:
        """
        写入Excel文件

        Args:
            data: 要写入的数据
            file_path: 输出文件路径
            sheet_name: 工作表名称
            index: 是否写入索引
        """
        try:
            file_path = Path(file_path)

            # 确保输出目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)

            logger.info(f"开始写入Excel文件: {file_path}")

            # 写入Excel文件
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                data.to_excel(writer, sheet_name=sheet_name, index=index)

            logger.info(f"成功写入Excel文件: {file_path}")

        except Exception as e:
            logger.error(f"写入Excel文件失败: {e}")
            raise

    def write_multiple_sheets(self, data_dict: Dict[str, pd.DataFrame],
                            file_path: str, index: bool = False) -> None:
        """
        写入多个工作表到Excel文件

        Args:
            data_dict: {工作表名: DataFrame} 字典
            file_path: 输出文件路径
            index: 是否写入索引
        """
        try:
            file_path = Path(file_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)

            logger.info(f"开始写入多工作表Excel文件: {file_path}")

            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                for sheet_name, df in data_dict.items():
                    df.to_excel(writer, sheet_name=sheet_name, index=index)

            logger.info(f"成功写入多工作表Excel文件: {file_path}")

        except Exception as e:
            logger.error(f"写入多工作表Excel文件失败: {e}")
            raise

    def _get_data_processor_template(self) -> str:
        """生成data_processor.py模板"""
        return '''"""
Data Processor
数据处理核心逻辑
"""

import pandas as pd
from typing import Dict, List, Any
from ..utils.logger import get_logger

logger = get_logger(__name__)


class DataProcessor:
    """数据处理器"""

    def __init__(self):
        pass

    def process_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        处理数据的主要方法

        Args:
            data: 输入数据

        Returns:
            pd.DataFrame: 处理后的数据
        """
        try:
            logger.info("开始处理数据")

            # TODO: 实现具体的数据处理逻辑
            processed_data = data.copy()

            logger.info("数据处理完成")
            return processed_data

        except Exception as e:
            logger.error(f"数据处理失败: {e}")
            raise

    def validate_data(self, data: pd.DataFrame) -> bool:
        """
        验证数据格式

        Args:
            data: 要验证的数据

        Returns:
            bool: 验证结果
        """
        try:
            # TODO: 实现数据验证逻辑
            return True
        except Exception as e:
            logger.error(f"数据验证失败: {e}")
            return False

    def _get_config_template(self) -> str:
        """生成config.py模板"""
        return '''"""
Config
配置文件管理
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any


class Config:
    """配置管理器"""

    def __init__(self, config_file: str = "config.yaml"):
        self.config_file = config_file
        self.config_data = {}
        self.load_config()

    def load_config(self):
        """加载配置文件"""
        config_path = Path(self.config_file)

        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config_data = yaml.safe_load(f) or {}
        else:
            # 创建默认配置
            self.config_data = self.get_default_config()
            self.save_config()

    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'input': {
                'default_path': './input',
                'supported_formats': ['.xlsx', '.xls', '.xlsm']
            },
            'output': {
                'default_path': './output',
                'backup_enabled': True,
                'backup_path': './backup'
            },
            'processing': {
                'batch_size': 1000,
                'parallel_processing': False
            },
            'logging': {
                'level': 'INFO',
                'file': 'excel2excel.log',
                'max_size': '10MB',
                'backup_count': 5
            }
        }

    def save_config(self):
        """保存配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(self.config_data, f, default_flow_style=False,
                     allow_unicode=True, indent=2)

    def get(self, key: str, default=None):
        """获取配置值"""
        keys = key.split('.')
        value = self.config_data

        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default

        return value

    def set(self, key: str, value: Any):
        """设置配置值"""
        keys = key.split('.')
        config = self.config_data

        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]

        config[keys[-1]] = value
        self.save_config()


# 全局配置实例
config = Config()
'''

    def _get_logger_template(self) -> str:
        """生成logger.py模板"""
        return '''"""
Logger
日志处理模块
"""

import logging
import logging.handlers
from pathlib import Path
from typing import Optional


def setup_logger(name: str = 'excel2excel',
                log_file: str = 'excel2excel.log',
                level: str = 'INFO') -> logging.Logger:
    """
    设置日志记录器

    Args:
        name: 日志记录器名称
        log_file: 日志文件路径
        level: 日志级别

    Returns:
        logging.Logger: 配置好的日志记录器
    """
    logger = logging.getLogger(name)

    # 避免重复添加处理器
    if logger.handlers:
        return logger

    # 设置日志级别
    log_level = getattr(logging, level.upper(), logging.INFO)
    logger.setLevel(log_level)

    # 创建格式器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 文件处理器
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)

        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
        )
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    return logger


def get_logger(name: str) -> logging.Logger:
    """
    获取日志记录器

    Args:
        name: 日志记录器名称

    Returns:
        logging.Logger: 日志记录器
    """
    return logging.getLogger(name)


# 设置默认日志记录器
default_logger = setup_logger()
'''

    def _get_validators_template(self) -> str:
        """生成validators.py模板"""
        return '''"""
Validators
数据验证模块
"""

import pandas as pd
from typing import List, Dict, Any, Optional
from ..utils.logger import get_logger

logger = get_logger(__name__)


class DataValidator:
    """数据验证器"""

    def __init__(self):
        pass

    def validate_excel_structure(self, data: pd.DataFrame,
                                required_columns: List[str]) -> bool:
        """
        验证Excel数据结构

        Args:
            data: 要验证的数据
            required_columns: 必需的列名列表

        Returns:
            bool: 验证结果
        """
        try:
            missing_columns = set(required_columns) - set(data.columns)

            if missing_columns:
                logger.error(f"缺少必需的列: {missing_columns}")
                return False

            logger.info("数据结构验证通过")
            return True

        except Exception as e:
            logger.error(f"数据结构验证失败: {e}")
            return False

    def validate_data_types(self, data: pd.DataFrame,
                           type_mapping: Dict[str, str]) -> bool:
        """
        验证数据类型

        Args:
            data: 要验证的数据
            type_mapping: 列名到数据类型的映射

        Returns:
            bool: 验证结果
        """
        try:
            for column, expected_type in type_mapping.items():
                if column in data.columns:
                    # TODO: 实现具体的数据类型验证逻辑
                    pass

            logger.info("数据类型验证通过")
            return True

        except Exception as e:
            logger.error(f"数据类型验证失败: {e}")
            return False
'''

    def _get_gui_template(self) -> str:
        """生成main_window.py模板"""
        return '''"""
Main Window
GUI主界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from pathlib import Path
from ..utils.logger import get_logger

logger = get_logger(__name__)


class MainWindow:
    """主窗口类"""

    def __init__(self):
        self.root = tk.Tk()
        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        self.root.title("Excel2Excel 数据转换工具")
        self.root.geometry("800x600")

        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="10")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 输入文件
        ttk.Label(file_frame, text="输入文件:").grid(row=0, column=0, sticky=tk.W)
        self.input_file_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.input_file_var, width=50).grid(row=0, column=1, padx=(10, 5))
        ttk.Button(file_frame, text="浏览", command=self.browse_input_file).grid(row=0, column=2)

        # 输出文件
        ttk.Label(file_frame, text="输出文件:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        self.output_file_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.output_file_var, width=50).grid(row=1, column=1, padx=(10, 5), pady=(10, 0))
        ttk.Button(file_frame, text="浏览", command=self.browse_output_file).grid(row=1, column=2, pady=(10, 0))

        # 处理按钮
        process_frame = ttk.Frame(main_frame)
        process_frame.grid(row=1, column=0, columnspan=2, pady=10)

        ttk.Button(process_frame, text="开始处理", command=self.process_file).pack(side=tk.LEFT, padx=5)
        ttk.Button(process_frame, text="清空", command=self.clear_fields).pack(side=tk.LEFT, padx=5)

        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="处理日志", padding="10")
        log_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

        self.log_text = tk.Text(log_frame, height=15, width=80)
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

    def browse_input_file(self):
        """浏览输入文件"""
        filename = filedialog.askopenfilename(
            title="选择输入Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls *.xlsm"), ("All files", "*.*")]
        )
        if filename:
            self.input_file_var.set(filename)

    def browse_output_file(self):
        """浏览输出文件"""
        filename = filedialog.asksaveasfilename(
            title="选择输出Excel文件",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if filename:
            self.output_file_var.set(filename)

    def process_file(self):
        """处理文件"""
        input_file = self.input_file_var.get()
        output_file = self.output_file_var.get()

        if not input_file or not output_file:
            messagebox.showerror("错误", "请选择输入和输出文件")
            return

        try:
            self.log_message("开始处理文件...")
            # TODO: 实现文件处理逻辑
            self.log_message("文件处理完成")
            messagebox.showinfo("成功", "文件处理完成")
        except Exception as e:
            error_msg = f"处理文件时出错: {e}"
            self.log_message(error_msg)
            messagebox.showerror("错误", error_msg)

    def clear_fields(self):
        """清空字段"""
        self.input_file_var.set("")
        self.output_file_var.set("")
        self.log_text.delete(1.0, tk.END)

    def log_message(self, message: str):
        """记录日志消息"""
        self.log_text.insert(tk.END, f"{message}\\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def run(self):
        """运行应用程序"""
        logger.info("启动GUI应用程序")
        self.root.mainloop()
'''


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="项目结构自动创建工具")
    parser.add_argument("--structure-file", "-s", default="项目目录结构.md",
                       help="项目结构文档文件路径")
    parser.add_argument("--output-dir", "-o", default=None,
                       help="输出目录路径，默认为当前目录")
    parser.add_argument("--dry-run", "-d", action="store_true",
                       help="仅显示将要创建的文件，不实际创建")

    args = parser.parse_args()

    try:
        creator = ProjectStructureCreator(args.structure_file)

        if args.dry_run:
            print("将要创建的文件结构:")
            items = creator.parse_structure_file()
            for file_path, file_name, comment in items:
                full_path = f"{file_path}/{file_name}" if file_path else file_name
                print(f"  {full_path} # {comment}")
        else:
            creator.create_structure(args.output_dir)
            print("项目结构创建完成!")

    except Exception as e:
        print(f"错误: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
